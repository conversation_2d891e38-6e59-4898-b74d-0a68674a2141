import express, { Request, Response } from "express";
import { supabaseClient } from "../middleware/supabaseMiddleware";
import { z } from "zod";
import { 
  storeUser<PERSON><PERSON><PERSON><PERSON>, 
  getUserApi<PERSON>ey, 
  deleteUserApi<PERSON>ey,
  getEphemeralUserCredentials 
} from "../middleware/apiKeyStorage";

const router = express.Router();

// Validation schemas
const storeCredentialsSchema = z.object({
  provider: z.string().min(1, "Provider is required"),
  apiKey: z.string().min(1, "API key is required"),
  baseUrl: z.string().url("Valid base URL is required"),
  extractionModel: z.string().min(1, "Extraction model is required"),
  generationModel: z.string().min(1, "Generation model is required")
});

const getCredentialsSchema = z.object({
  provider: z.string().min(1, "Provider is required")
});

// Helper function to verify the current user is authenticated
async function getAuthenticatedUser(
  req: Request
): Promise<
  | { error: string; details?: string; status: number }
  | { user: { id: string; [key: string]: any } }
> {
  try {
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return { error: "Unauthorized: Missing or malformed token", status: 401 };
    }

    const token = authHeader.split(" ")[1];
    if (!token) {
      return { error: "Unauthorized: Missing token", status: 401 };
    }

    const supabase = supabaseClient;
    const { data, error: getUserError } = await supabase.auth.getUser(token);

    if (getUserError) {
      if (
        getUserError.message.toLowerCase().includes("invalid token") ||
        getUserError.message.includes("jwt")
      ) {
        return {
          error: "Unauthorized: Invalid token",
          details: getUserError.message,
          status: 401,
        };
      }
      return {
        error: "Server error validating token",
        details: getUserError.message,
        status: 500,
      };
    }

    const user = data?.user;
    if (!user) {
      return { error: "Unauthorized: No user found for token", status: 401 };
    }

    if (!user.id) {
      return { error: "User ID missing from authenticated user", status: 500 };
    }

    return { user: { ...user } };
  } catch (err: any) {
    console.error("Auth error in credentialsRoutes:", err.message, err.stack);
    return { error: "Authentication error", status: 500 };
  }
}

// POST /api/credentials - Store user's AI provider credentials securely
router.post("/", async (req: Request, res: Response) => {
  console.log('POST /api/credentials - Body keys:', Object.keys(req.body));

  const authResult = await getAuthenticatedUser(req);
  if ("error" in authResult) {
    console.log('❌ Authentication failed:', authResult.error);
    return res
      .status(authResult.status)
      .json({ error: authResult.error, details: authResult.details });
  }
  const user = authResult.user;
  console.log('✅ User authenticated:', user.id);

  try {
    const validationResult = storeCredentialsSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res
        .status(400)
        .json({ 
          error: "Invalid request data", 
          details: validationResult.error.flatten() 
        });
    }

    const { provider, apiKey, baseUrl, extractionModel, generationModel } = validationResult.data;
    console.log('📝 Storing credentials for provider:', provider);

    const result = await storeUserApiKey(
      user.id,
      provider,
      apiKey,
      baseUrl,
      { extraction: extractionModel, generation: generationModel }
    );

    if (!result.success) {
      console.log('❌ Failed to store credentials:', result.error);
      return res.status(500).json({
        error: "Failed to store credentials",
        details: result.error
      });
    }

    console.log('✅ Credentials stored successfully');
    return res.status(200).json({
      success: true,
      message: "Credentials stored securely"
    });
  } catch (error: any) {
    console.error("Error storing credentials:", error);
    return res
      .status(500)
      .json({ error: "Failed to store credentials", details: error.message });
  }
});

// GET /api/credentials/:provider - Get user's stored credentials (without API key)
router.get("/:provider", async (req: Request, res: Response) => {
  console.log(`GET /api/credentials/${req.params.provider} - Body keys:`, Object.keys(req.body));

  const authResult = await getAuthenticatedUser(req);
  if ("error" in authResult) {
    console.log('❌ Authentication failed in GET credentials:', authResult.error);
    return res
      .status(authResult.status)
      .json({ error: authResult.error, details: authResult.details });
  }
  const user = authResult.user;
  console.log('✅ User authenticated for GET credentials:', user.id);

  try {
    const { provider } = req.params;
    console.log(`🔍 Retrieving credentials for provider: ${provider}, user: ${user.id}`);

    if (!provider) {
      console.log('❌ Provider parameter missing');
      return res.status(400).json({ error: "Provider parameter is required" });
    }

    const result = await getUserApiKey(user.id, provider);
    console.log(`📋 getUserApiKey result:`, {
      success: result.success,
      hasCredentials: !!result.credentials,
      error: result.error
    });

    if (!result.success) {
      if (result.error === 'No credentials found for this provider') {
        console.log(`📭 No credentials found for provider: ${provider}`);
        return res.status(404).json({
          success: false,
          error: "No credentials found",
          hasCredentials: false
        });
      }
      console.error(`❌ Failed to retrieve credentials for ${provider}:`, result.error);
      return res.status(500).json({
        success: false,
        error: "Failed to retrieve credentials",
        details: result.error
      });
    }

    console.log(`✅ Successfully retrieved credentials for ${provider}`);
    // Return configuration without the actual API key
    return res.status(200).json({
      success: true,
      hasCredentials: true,
      configuration: {
        provider,
        baseUrl: result.credentials!.baseUrl,
        extractionModel: result.credentials!.extractionModel,
        generationModel: result.credentials!.generationModel
      }
    });
  } catch (error: any) {
    console.error(`❌ Error retrieving credentials for ${req.params.provider}:`, {
      error: error.message,
      stack: error.stack,
      userId: user.id
    });
    return res
      .status(500)
      .json({
        success: false,
        error: "Failed to retrieve credentials",
        details: error.message
      });
  }
});

// GET /api/credentials/:provider/ephemeral - Get user's credentials for backend use (includes decrypted API key)
router.get("/:provider/ephemeral", async (req: Request, res: Response) => {
  const authResult = await getAuthenticatedUser(req);
  if ("error" in authResult) {
    return res
      .status(authResult.status)
      .json({ error: authResult.error, details: authResult.details });
  }
  const user = authResult.user;

  try {
    const { provider } = req.params;

    if (!provider) {
      return res.status(400).json({ error: "Provider parameter is required" });
    }

    const result = await getEphemeralUserCredentials(user.id, provider);

    if (!result.success) {
      if (result.error === 'No credentials found for this provider') {
        return res.status(404).json({
          error: "No credentials found",
          hasCredentials: false
        });
      }
      return res.status(500).json({
        error: "Failed to retrieve credentials",
        details: result.error
      });
    }

    // Return full configuration including decrypted API key for backend use
    return res.status(200).json({
      success: true,
      hasCredentials: true,
      credentials: {
        provider,
        apiKey: result.credentials!.apiKey,
        baseUrl: result.credentials!.baseUrl,
        extractionModel: result.credentials!.extractionModel,
        generationModel: result.credentials!.generationModel
      }
    });
  } catch (error: any) {
    console.error("Error retrieving ephemeral credentials:", error);
    return res
      .status(500)
      .json({ error: "Failed to retrieve credentials", details: error.message });
  }
});

// DELETE /api/credentials/:provider - Delete user's stored credentials
router.delete("/:provider", async (req: Request, res: Response) => {
  const authResult = await getAuthenticatedUser(req);
  if ("error" in authResult) {
    return res
      .status(authResult.status)
      .json({ error: authResult.error, details: authResult.details });
  }
  const user = authResult.user;

  try {
    const { provider } = req.params;
    
    if (!provider) {
      return res.status(400).json({ error: "Provider parameter is required" });
    }

    const result = await deleteUserApiKey(user.id, provider);

    if (!result.success) {
      return res.status(500).json({ 
        error: "Failed to delete credentials", 
        details: result.error 
      });
    }

    return res.status(200).json({ 
      success: true, 
      message: "Credentials deleted successfully" 
    });
  } catch (error: any) {
    console.error("Error deleting credentials:", error);
    return res
      .status(500)
      .json({ error: "Failed to delete credentials", details: error.message });
  }
});

// Internal endpoint for getting ephemeral credentials (used by AI routes)
// This should not be exposed publicly - only used internally by other routes
export async function getEphemeralCredentialsForUser(
  userId: string, 
  provider: string
) {
  return getEphemeralUserCredentials(userId, provider);
}

export default router;
