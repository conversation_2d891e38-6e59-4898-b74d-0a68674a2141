import { AIProviderSettings } from "@/types";

/**
 * Set AI provider settings (excluding API key which is stored securely on server)
 */
export function setAIProviderSettings(settings: Omit<AIProviderSettings, 'apiKey'>): void {
  // Store only non-sensitive configuration in localStorage
  const safeSettings = {
    provider: settings.provider,
    baseUrl: settings.baseUrl,
    extractionModel: settings.extractionModel,
    generationModel: settings.generationModel,
    model: settings.generationModel, // Keep model field aligned with generationModel
    // Never store apiKey in localStorage
  };
  localStorage.setItem("aiProviderSettings", JSON.stringify(safeSettings));
}

/**
 * Get AI provider settings from localStorage
 */
export function getAIProviderSettings(): AIProviderSettings {
  const systemDefaultProvider = "OpenRouter";
  const systemDefaultBaseUrl = getDefaultBaseUrl(systemDefaultProvider);
  const systemDefaultModels = getAvailableModels(systemDefaultProvider);

  const defaultExtractionModelId = "google/gemini-2.5-flash-preview-05-20";
  const defaultGenerationModelId = "google/gemini-2.5-pro-preview";

  const systemDefaultExtractionModel =
    systemDefaultModels.find((m) => m.id === defaultExtractionModelId)?.id ||
    systemDefaultModels[0]?.id ||
    "";
  const systemDefaultGenerationModel =
    systemDefaultModels.find((m) => m.id === defaultGenerationModelId)?.id ||
    systemDefaultModels[0]?.id ||
    "";

  const systemDefaults: AIProviderSettings = {
    provider: systemDefaultProvider,
    baseUrl: systemDefaultBaseUrl,
    apiKey: "", // Never use environment variables for API keys on client-side
    extractionModel: systemDefaultExtractionModel,
    generationModel: systemDefaultGenerationModel,
    model: systemDefaultGenerationModel, // Legacy field, matches generationModel
  };

  const storedSettings = localStorage.getItem("aiProviderSettings");
  if (!storedSettings) {
    return systemDefaults;
  }

  try {
    const parsedSettings = JSON.parse(
      storedSettings
    ) as Partial<AIProviderSettings>;

    const currentProvider = parsedSettings.provider || systemDefaults.provider;
    const modelsForCurrentProvider = getAvailableModels(currentProvider);

    let finalExtractionModel = parsedSettings.extractionModel;
    if (
      !finalExtractionModel ||
      !modelsForCurrentProvider.find((m) => m.id === finalExtractionModel)
    ) {
      finalExtractionModel =
        modelsForCurrentProvider.find(
          (m) => m.id === systemDefaults.extractionModel
        )?.id ||
        modelsForCurrentProvider[0]?.id ||
        "";
    }

    let finalGenerationModel = parsedSettings.generationModel;
    if (
      !finalGenerationModel ||
      !modelsForCurrentProvider.find((m) => m.id === finalGenerationModel)
    ) {
      finalGenerationModel =
        modelsForCurrentProvider.find(
          (m) => m.id === systemDefaults.generationModel
        )?.id ||
        modelsForCurrentProvider[0]?.id ||
        "";
    }

    return {
      provider: currentProvider,
      baseUrl:
        parsedSettings.baseUrl ||
        getDefaultBaseUrl(currentProvider) ||
        systemDefaults.baseUrl,
      apiKey: "", // API keys are never stored in localStorage - retrieved from server when needed
      extractionModel: finalExtractionModel,
      generationModel: finalGenerationModel,
      model: finalGenerationModel, // Legacy field always matches generationModel
    };
  } catch (e) {
    // console.error("Failed to parse AI provider settings, returning system defaults:", e);
    return systemDefaults;
  }
}

/**
 * Check if AI provider settings are configured
 * This function checks if credentials are stored in the backend
 */
export async function isAIProviderConfigured(): Promise<boolean> {
  try {
    console.log('🔍 isAIProviderConfigured: Starting AI provider configuration check');

    const settings = getAIProviderSettings();
    console.log('⚙️ isAIProviderConfigured: Current settings:', {
      provider: settings.provider,
      hasBaseUrl: !!settings.baseUrl,
      hasExtractionModel: !!settings.extractionModel,
      hasGenerationModel: !!settings.generationModel
    });

    if (!settings.provider) {
      console.log('❌ isAIProviderConfigured: No provider configured in settings');
      return false;
    }

    // Import the API function dynamically to avoid circular dependencies
    const { getCredentialsAPI } = await import('./api');

    console.log(`🔑 isAIProviderConfigured: Checking backend credentials for provider: ${settings.provider}`);

    // Check if credentials exist in the backend for the current provider
    const credentialsResult = await getCredentialsAPI(settings.provider);

    const isConfigured = credentialsResult.success && credentialsResult.hasCredentials;
    console.log(`✅ isAIProviderConfigured: Result for ${settings.provider}:`, {
      success: credentialsResult.success,
      hasCredentials: credentialsResult.hasCredentials,
      isConfigured
    });

    return isConfigured;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error('❌ isAIProviderConfigured: Error checking AI provider configuration:', {
      error: errorMessage,
      stack: error instanceof Error ? error.stack : undefined
    });

    // Return false on error to fall back to basic text extraction
    return false;
  }
}

/**
 * Synchronous version that checks localStorage only (for backward compatibility)
 * This should be used sparingly and eventually replaced with the async version
 * @deprecated Use isAIProviderConfigured() instead
 */
export function isAIProviderConfiguredSync(): boolean {
  const settings = getAIProviderSettings();
  // Check if we have basic configuration (provider, models, etc.)
  return Boolean(
    settings.provider &&
    settings.extractionModel &&
    settings.generationModel &&
    settings.baseUrl
  );
}



/**
 * Get AI provider settings with API key from backend
 * This function retrieves the complete configuration including the decrypted API key
 */
export async function getAIProviderSettingsWithApiKey(): Promise<AIProviderSettings & { apiKey: string }> {
  try {
    const localSettings = getAIProviderSettings();

    // Import the API function dynamically to avoid circular dependencies
    const { getCredentialsAPI } = await import('./api');

    // Get credentials from backend
    const credentialsResult = await getCredentialsAPI(localSettings.provider);

    if (!credentialsResult.success || !credentialsResult.hasCredentials || !credentialsResult.configuration) {
      throw new Error('No credentials found for the current provider');
    }

    // For now, we'll need to get the API key from the backend when making actual API calls
    // The backend will handle decryption and use the key directly
    return {
      ...localSettings,
      apiKey: '', // API key will be handled by backend
      provider: credentialsResult.configuration.provider,
      baseUrl: credentialsResult.configuration.baseUrl,
      extractionModel: credentialsResult.configuration.extractionModel,
      generationModel: credentialsResult.configuration.generationModel,
      model: credentialsResult.configuration.generationModel,
    };
  } catch (error) {
    console.error('Error getting AI provider settings with API key:', error);
    throw error;
  }
}

/**
 * Get list of available AI models by provider
 */
export function getAvailableModels(
  provider: string
): { id: string; name: string }[] {
  switch (provider) {
    case "OpenRouter":
      return [
        { id: "google/gemini-2.5-flash-preview-05-20", name: "Google Gemini Flash 2.5" },
        {
          id: "google/gemini-2.5-pro-preview",
          name: "Google Gemini Pro 2.5 (Recommended)",
        },
      ];
    default:
      return [];
  }
}

/**
 * Get available AI providers
 */
export function getAvailableProviders(): string[] {
  return ["OpenRouter"];
}

/**
 * Get default base URL for a provider
 */
export function getDefaultBaseUrl(provider: string): string {
  switch (provider) {
    case "OpenRouter":
      return "https://openrouter.ai/api/v1";
    default:
      return "";
  }
}

/**
 * Get recommended model for specific tasks
 * This follows the PRD recommendations
 */
export function getRecommendedModelForTask(
  task: "extraction" | "generation"
): string {
  const settings = getAIProviderSettings();
  switch (task) {
    case "extraction":
      return settings.extractionModel || "google/gemini-2.5-flash-preview-05-20";
    case "generation":
      return settings.generationModel || "google/gemini-2.5-pro-preview";
    default:
      return settings.generationModel || "google/gemini-2.5-pro-preview";
  }
}
